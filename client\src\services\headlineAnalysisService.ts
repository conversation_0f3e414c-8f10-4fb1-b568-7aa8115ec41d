import { supabase } from '@/lib/supabase'

/**
 * Interface for headline analysis data
 */
export interface HeadlineAnalysis {
  id: string
  created_at: string
  updated_at: string
  user_id: string
  headline_text: string
  content_type: string
  audience_context?: string
  tool_type: string
  analysis_version: string
  overall_score: number
  basic_analysis: any // JSONB
  advanced_analysis: any // JSONB
  recommendations: any // JSONB
  analysis_duration_ms?: number
  status: 'processing' | 'completed' | 'failed'
  error_message?: string
  is_favorite: boolean
  custom_name?: string
  tags: string[]
  notes?: string
  view_count: number
  last_viewed_at?: string
  regeneration_count: number
}

/**
 * Interface for creating new headline analysis
 */
export interface CreateHeadlineAnalysisData {
  user_id: string
  headline_text: string
  content_type: string
  audience_context?: string
  tool_type?: string
  analysis_version?: string
  overall_score: number
  basic_analysis: any
  advanced_analysis: any
  recommendations: any
  analysis_duration_ms?: number
  status?: 'processing' | 'completed' | 'failed'
  error_message?: string
  is_favorite?: boolean
  custom_name?: string
  tags?: string[]
  notes?: string
}

/**
 * Interface for updating headline analysis
 */
export interface UpdateHeadlineAnalysisData {
  id: string
  custom_name?: string
  is_favorite?: boolean
  tags?: string[]
  notes?: string
  view_count?: number
  last_viewed_at?: string
  regeneration_count?: number
}

/**
 * Service for managing headline analysis data in Supabase
 */
export class HeadlineAnalysisService {

  /**
   * Save a new headline analysis to the database
   */
  async saveAnalysis(analysisData: CreateHeadlineAnalysisData): Promise<HeadlineAnalysis> {
    try {
      console.log('💾 Saving headline analysis:', {
        user_id: analysisData.user_id,
        headline_text: analysisData.headline_text?.substring(0, 50) + '...',
        overall_score: analysisData.overall_score
      });

      // Check for duplicate analysis before saving
      if (analysisData.user_id && analysisData.headline_text) {
        const existingAnalyses = await this.getUserAnalyses(analysisData.user_id, {
          limit: 5,
          orderBy: 'created_at',
          orderDirection: 'desc'
        });

        // Check if the same headline was analyzed recently (within last 5 minutes)
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        const duplicateAnalysis = existingAnalyses.find(analysis => 
          analysis.headline_text === analysisData.headline_text &&
          new Date(analysis.created_at) > fiveMinutesAgo
        );

        if (duplicateAnalysis) {
          console.log('⚠️ Duplicate analysis detected, returning existing analysis');
          return duplicateAnalysis;
        }
      }

      // Ensure history limit: keep only last 9 analyses (so new one makes 10)
      await this.enforceHistoryLimit(analysisData.user_id);

      const { data, error } = await supabase
        .from('headline_analyses')
        .insert(analysisData)
        .select()
        .single();

      if (error) {
        console.error('❌ Error saving headline analysis:', error);
        throw new Error(`Failed to save analysis: ${error.message}`);
      }

      console.log('✅ Headline analysis saved successfully:', data.id);
      return data;
    } catch (error) {
      console.error('💥 Exception in saveAnalysis:', error);
      throw error;
    }
  }

  /**
   * Enforce history limit: keep only last 9 non-favorite analyses
   */
  private async enforceHistoryLimit(userId: string): Promise<void> {
    try {
      // Get non-favorite analyses ordered by creation date (newest first)
      const { data: analyses, error } = await supabase
        .from('headline_analyses')
        .select('id, created_at')
        .eq('user_id', userId)
        .eq('is_favorite', false)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching analyses for history limit:', error);
        return;
      }

      // If we have 10 or more non-favorite analyses, delete the oldest ones
      if (analyses && analyses.length >= 10) {
        const analysesToDelete = analyses.slice(9); // Keep first 9, delete the rest
        const idsToDelete = analysesToDelete.map(a => a.id);

        if (idsToDelete.length > 0) {
          const { error: deleteError } = await supabase
            .from('headline_analyses')
            .delete()
            .in('id', idsToDelete);

          if (deleteError) {
            console.error('❌ Error deleting old analyses:', deleteError);
          } else {
            console.log(`🗑️ Deleted ${idsToDelete.length} old analyses to maintain history limit`);
          }
        }
      }
    } catch (error) {
      console.error('💥 Exception in enforceHistoryLimit:', error);
      // Don't throw - this is a cleanup operation that shouldn't block saving
    }
  }

  /**
   * Get all headline analyses for the current user
   */
  async getUserAnalyses(userId: string, options?: {
    limit?: number
    offset?: number
    toolType?: string
    isFavorite?: boolean
    orderBy?: 'created_at' | 'updated_at' | 'overall_score'
    orderDirection?: 'asc' | 'desc'
  }): Promise<HeadlineAnalysis[]> {
    try {
      console.log('🔍 getUserAnalyses called with:', { userId, options });

      // Validate user ID
      if (!userId || userId === 'anonymous') {
        console.warn('⚠️ Invalid user ID provided to getUserAnalyses');
        return [];
      }

      // Check authentication first
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        console.error('❌ Authentication error in getUserAnalyses:', authError);
        throw new Error('User not authenticated');
      }

      if (user.id !== userId) {
        console.error('❌ User ID mismatch in getUserAnalyses:', { requestedUserId: userId, actualUserId: user.id });
        throw new Error('User ID mismatch');
      }

      // Build query with explicit column selection
      let query = supabase
        .from('headline_analyses')
        .select(`
          id,
          created_at,
          updated_at,
          user_id,
          headline_text,
          content_type,
          audience_context,
          tool_type,
          analysis_version,
          overall_score,
          basic_analysis,
          advanced_analysis,
          recommendations,
          analysis_duration_ms,
          status,
          error_message,
          is_favorite,
          custom_name,
          tags,
          notes,
          view_count,
          last_viewed_at,
          regeneration_count
        `)
        .eq('user_id', userId);

      // Apply filters
      if (options?.toolType) {
        query = query.eq('tool_type', options.toolType);
      }
      if (options?.isFavorite !== undefined) {
        query = query.eq('is_favorite', options.isFavorite);
      }

      // Apply ordering
      const orderBy = options?.orderBy || 'created_at';
      const orderDirection = options?.orderDirection || 'desc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      console.log('🔍 Executing Supabase query for getUserAnalyses...');
      const { data, error } = await query;

      if (error) {
        console.error('❌ Supabase error in getUserAnalyses:', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          userId,
          options
        });

        if (error.code === 'PGRST116') {
          throw new Error('Table not found. Please ensure the headline_analyses table exists.');
        } else {
          throw new Error(`Failed to fetch analyses: ${error.message}`);
        }
      }

      console.log('✅ getUserAnalyses successful:', {
        userId,
        analysesCount: data?.length || 0,
        options
      });

      return data || [];
    } catch (error) {
      console.error('💥 Exception in getUserAnalyses:', error);
      throw error;
    }
  }

  /**
   * Get a specific headline analysis by ID
   */
  async getAnalysisById(id: string): Promise<HeadlineAnalysis | null> {
    const { data, error } = await supabase
      .from('headline_analyses')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw new Error(`Failed to fetch analysis: ${error.message}`);
    }

    return data;
  }

  /**
   * Update a headline analysis
   */
  async updateAnalysis(updateData: UpdateHeadlineAnalysisData): Promise<HeadlineAnalysis> {
    const { id, ...updates } = updateData;

    const { data, error } = await supabase
      .from('headline_analyses')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update analysis: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete a headline analysis
   */
  async deleteAnalysis(id: string): Promise<void> {
    const { error } = await supabase
      .from('headline_analyses')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete analysis: ${error.message}`);
    }
  }

  /**
   * Get recent headline analyses (last 10)
   */
  async getRecentAnalyses(): Promise<HeadlineAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    return this.getUserAnalyses(user.id, {
      limit: 10,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
  }

  /**
   * Get favorite headline analyses
   */
  async getFavoriteAnalyses(): Promise<HeadlineAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    return this.getUserAnalyses(user.id, {
      isFavorite: true,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
  }

  /**
   * Toggle favorite status of an analysis
   */
  async toggleFavorite(id: string): Promise<HeadlineAnalysis> {
    // First get the current analysis to toggle its favorite status
    const currentAnalysis = await this.getAnalysisById(id);
    if (!currentAnalysis) {
      throw new Error('Analysis not found');
    }

    const { data, error } = await supabase
      .from('headline_analyses')
      .update({ is_favorite: !currentAnalysis.is_favorite })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to toggle favorite: ${error.message}`);
    }

    return data;
  }

  /**
   * Rename an analysis
   */
  async renameAnalysis(id: string, newName: string): Promise<HeadlineAnalysis> {
    const { data, error } = await supabase
      .from('headline_analyses')
      .update({ custom_name: newName.trim() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to rename analysis: ${error.message}`);
    }

    return data;
  }

  /**
   * Record view for an analysis
   */
  async recordView(id: string): Promise<void> {
    const currentAnalysis = await this.getAnalysisById(id);
    if (!currentAnalysis) {
      return;
    }

    const { error } = await supabase
      .from('headline_analyses')
      .update({
        view_count: (currentAnalysis.view_count || 0) + 1,
        last_viewed_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      console.error('Failed to record view:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Add tags to an analysis
   */
  async updateTags(id: string, tags: string[]): Promise<HeadlineAnalysis> {
    const { data, error } = await supabase
      .from('headline_analyses')
      .update({ tags })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update tags: ${error.message}`);
    }

    return data;
  }

  /**
   * Add notes to an analysis
   */
  async updateNotes(id: string, notes: string): Promise<HeadlineAnalysis> {
    const { data, error } = await supabase
      .from('headline_analyses')
      .update({ notes })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update notes: ${error.message}`);
    }

    return data;
  }

  /**
   * Get analysis statistics for a user
   */
  async getUserStats(userId: string): Promise<{
    totalAnalyses: number
    favoriteAnalyses: number
    averageScore: number
    contentTypeBreakdown: Record<string, number>
    recentActivity: number // analyses in last 7 days
  }> {
    try {
      console.log('📊 Getting user stats for:', userId);

      const analyses = await this.getUserAnalyses(userId, {
        limit: 1000, // Get all analyses for stats
        orderBy: 'created_at',
        orderDirection: 'desc'
      });

      const totalAnalyses = analyses.length;
      const favoriteAnalyses = analyses.filter(a => a.is_favorite).length;

      // Calculate average score
      const totalScore = analyses.reduce((sum, a) => sum + (a.overall_score || 0), 0);
      const averageScore = totalAnalyses > 0 ? totalScore / totalAnalyses : 0;

      // Content type breakdown
      const contentTypeBreakdown: Record<string, number> = {};
      analyses.forEach(analysis => {
        const contentType = analysis.content_type || 'unknown';
        contentTypeBreakdown[contentType] = (contentTypeBreakdown[contentType] || 0) + 1;
      });

      // Recent activity (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const recentActivity = analyses.filter(a =>
        new Date(a.created_at) > sevenDaysAgo
      ).length;

      console.log('✅ getUserStats successful:', {
        totalAnalyses,
        favoriteAnalyses,
        averageScore: Math.round(averageScore * 100) / 100,
        contentTypeBreakdown,
        recentActivity
      });

      return {
        totalAnalyses,
        favoriteAnalyses,
        averageScore: Math.round(averageScore * 100) / 100,
        contentTypeBreakdown,
        recentActivity
      };
    } catch (error) {
      console.error('💥 Exception in getUserStats:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const headlineAnalysisService = new HeadlineAnalysisService()
