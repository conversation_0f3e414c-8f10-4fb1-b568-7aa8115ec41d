// Test script to verify color palette saving functionality
// Run this in the browser console on the color palette generator page

async function testPaletteSave() {
  console.log('🎨 Testing Color Palette Save Functionality...');

  // Test data matching the format from the frontend
  const testPaletteData = {
    name: 'Test Palette Browser',
    colors: ['#FF5733', '#33FF57', '#3357FF', '#F39C12'],
    description: 'Test description for palette save from browser',
    tags: [],
    is_favorite: false
  };

  console.log('📤 Sending test palette data:', testPaletteData);

  try {
    // Get auth token from localStorage or sessionStorage if available
    const authToken = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');

    const headers = {
      'Content-Type': 'application/json',
    };

    // Add auth header if token is available
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
      console.log('🔐 Using auth token from storage');
    } else {
      console.log('⚠️ No auth token found - this may fail with 401');
    }

    // Make the API call to save the palette
    const response = await fetch('/api/palettes', {
      method: 'POST',
      headers,
      body: JSON.stringify(testPaletteData)
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Palette saved successfully:', result);
      return { success: true, data: result };
    } else {
      const errorText = await response.text();
      console.error('❌ Failed to save palette:', response.status, errorText);
      return { success: false, error: errorText, status: response.status };
    }
  } catch (error) {
    console.error('❌ Network error:', error);
    return { success: false, error: error.message };
  }
}

// Function to test if user is authenticated
async function checkAuthStatus() {
  try {
    const response = await fetch('/api/palettes?limit=1');
    console.log('🔐 Auth check status:', response.status);
    if (response.status === 401) {
      console.log('❌ User not authenticated');
      return false;
    } else if (response.status === 200) {
      console.log('✅ User is authenticated');
      return true;
    }
  } catch (error) {
    console.error('❌ Auth check failed:', error);
    return false;
  }
}

// Main test function
async function runPaletteTest() {
  console.log('🚀 Starting Color Palette Save Test...');

  // First check if user is authenticated
  const isAuthenticated = await checkAuthStatus();

  if (!isAuthenticated) {
    console.log('⚠️ Please log in first, then run the test again');
    return;
  }

  // Run the save test
  const result = await testPaletteSave();

  if (result.success) {
    console.log('🎉 Test completed successfully!');
  } else {
    console.log('💥 Test failed:', result.error);
  }
}

// Run the test
runPaletteTest();
