/**
 * Service for interacting with the moodboard API
 * Follows the same pattern as the visual complexity analyzer and palette service
 */

import { supabase } from '@/lib/supabase'
import { api } from '@/lib/api'
import type { Moodboard, MoodboardHistory } from '@/lib/supabase'

export interface CreateMoodboardRequest {
  title?: string
  description?: string
  tldraw_data?: any
  canvas_snapshot?: string
  tags?: string[]
  is_public?: boolean
  is_favorite?: boolean
  collaboration_enabled?: boolean
  shared_with?: string[]
  notes?: string
}

export interface UpdateMoodboardRequest {
  title?: string
  description?: string
  tldraw_data?: any
  canvas_snapshot?: string
  tags?: string[]
  is_public?: boolean
  is_favorite?: boolean
  collaboration_enabled?: boolean
  shared_with?: string[]
  status?: string
  notes?: string
}

export interface MoodboardListResponse {
  moodboards: Moodboard[]
  total_count: number
  page: number
  limit: number
}

export interface MoodboardOperationResponse {
  success: boolean
  message: string
  data?: any
  error?: string
}

export interface MoodboardStatsResponse {
  total_moodboards: number
  active_moodboards: number
  favorite_moodboards: number
  public_moodboards: number
  total_views: number
  recent_activity: Array<{
    id: string
    title: string
    updated_at: string
    action: string
  }>
}

class MoodboardService {

  async createMoodboard(data: CreateMoodboardRequest): Promise<MoodboardOperationResponse> {
    try {
      const response = await api.post('/api/moodboard/create', data)
      return response.data
    } catch (error) {
      console.error('Error creating moodboard:', error)
      throw error
    }
  }

  async updateMoodboard(id: string, data: UpdateMoodboardRequest): Promise<MoodboardOperationResponse> {
    try {
      const response = await api.put(`/api/moodboard/${id}`, data)
      return response.data
    } catch (error) {
      console.error('Error updating moodboard:', error)
      throw error
    }
  }

  async getMoodboard(id: string): Promise<MoodboardOperationResponse> {
    try {
      const response = await api.get(`/api/moodboard/${id}`)
      return response.data
    } catch (error) {
      console.error('Error getting moodboard:', error)
      throw error
    }
  }

  async listMoodboards(page: number = 1, limit: number = 20, status: string = 'active'): Promise<MoodboardListResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        status
      })

      const response = await api.get(`/api/moodboard/list?${params}`)
      return response.data
    } catch (error) {
      console.error('Error listing moodboards:', error)
      throw error
    }
  }

  async deleteMoodboard(id: string): Promise<MoodboardOperationResponse> {
    try {
      const response = await api.delete(`/api/moodboard/${id}`)
      return response.data
    } catch (error) {
      console.error('Error deleting moodboard:', error)
      throw error
    }
  }

  async createMoodboardHistory(
    moodboardId: string,
    data: {
      change_type: 'create' | 'update' | 'snapshot'
      change_description?: string
      tldraw_data_snapshot: any
      is_auto_save?: boolean
    }
  ): Promise<MoodboardOperationResponse> {
    try {
      const response = await api.post(`/api/moodboard/${moodboardId}/history`, data)
      return response.data
    } catch (error) {
      console.error('Error creating moodboard history:', error)
      throw error
    }
  }

  async getMoodboardStats(): Promise<MoodboardStatsResponse> {
    try {
      const response = await api.get('/api/moodboard/stats/summary')
      return response.data
    } catch (error) {
      console.error('Error getting moodboard stats:', error)
      throw error
    }
  }

  // Auto-save functionality
  async autoSaveMoodboard(id: string, tldrawData: any): Promise<void> {
    try {
      // Update the moodboard with new tldraw data
      await this.updateMoodboard(id, {
        tldraw_data: tldrawData
      })

      // Create a history entry for the auto-save
      await this.createMoodboardHistory(id, {
        change_type: 'update',
        change_description: 'Auto-save',
        tldraw_data_snapshot: tldrawData,
        is_auto_save: true
      })
    } catch (error) {
      console.error('Error auto-saving moodboard:', error)
      // Don't throw error for auto-save failures to avoid disrupting user experience
    }
  }
}

export const moodboardService = new MoodboardService()
export default moodboardService
