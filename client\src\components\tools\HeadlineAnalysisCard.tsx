import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Heart,
  Edit3,
  Trash2,
  Calendar,
  Eye,
  RefreshCw,
  Check,
  X,
  FileText,
  BarChart3,
  MessageSquare,
  Target
} from 'lucide-react';
import type { HeadlineAnalysis } from '@/lib/supabase';

// Componente para mostrar una tarjeta de análisis de título guardado
interface HeadlineAnalysisCardProps {
  analysis: HeadlineAnalysis;
  onLoad: () => void;
  onToggleFavorite: () => void;
  onRename: (newName: string) => void;
  onDelete: () => void;
  onRegenerate: () => void;
  isRenaming: boolean;
  onStartRename: () => void;
  onCancelRename: () => void;
  renameValue: string;
  onRenameValueChange: (value: string) => void;
}

function HeadlineAnalysisCard({
  analysis,
  onLoad,
  onToggleFavorite,
  onRename,
  onDelete,
  onRegenerate,
  isRenaming,
  onStartRename,
  onCancelRename,
  renameValue,
  onRenameValueChange,
}: HeadlineAnalysisCardProps) {

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDisplayName = () => {
    return analysis.custom_name || `Análisis ${formatDate(analysis.created_at)}`;
  };

  const handleRenameSubmit = () => {
    if (renameValue.trim()) {
      onRename(renameValue.trim());
    } else {
      onCancelRename();
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getContentTypeIcon = (contentType: string) => {
    switch (contentType) {
      case 'blog':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'social':
        return <MessageSquare className="h-4 w-4 text-purple-500" />;
      case 'email':
        return <Target className="h-4 w-4 text-green-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getContentTypeLabel = (contentType: string) => {
    switch (contentType) {
      case 'blog':
        return 'Blog';
      case 'social':
        return 'Redes Sociales';
      case 'email':
        return 'Email';
      case 'ad':
        return 'Publicidad';
      case 'news':
        return 'Noticias';
      case 'ecommerce':
        return 'E-commerce';
      default:
        return contentType;
    }
  };

  // Truncate headline text for display
  const truncateHeadline = (text: string, maxLength: number = 60) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <Card className="p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start gap-4">
        {/* Content Type Icon */}
        <div className="flex-shrink-0">
          <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 border border-gray-200 flex items-center justify-center">
            {getContentTypeIcon(analysis.content_type)}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {isRenaming ? (
            <div className="flex items-center gap-2 mb-2">
              <Input
                value={renameValue}
                onChange={(e) => onRenameValueChange(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleRenameSubmit();
                  if (e.key === 'Escape') onCancelRename();
                }}
                className="text-lg font-semibold"
                autoFocus
              />
              <Button size="sm" onClick={handleRenameSubmit}>
                <Check className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="outline" onClick={onCancelRename}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <h3 className="text-lg font-semibold text-gray-900 mb-2 truncate">
              {getDisplayName()}
            </h3>
          )}

          {/* Headline Text */}
          <div className="mb-3">
            <p className="text-sm font-medium text-gray-800 mb-1">
              "{truncateHeadline(analysis.headline_text)}"
            </p>
          </div>

          <div className="flex items-center gap-3 mb-3 flex-wrap">
            <div className="flex items-center gap-1">
              {getContentTypeIcon(analysis.content_type)}
              <span className="text-sm text-gray-600">
                {getContentTypeLabel(analysis.content_type)}
              </span>
            </div>
            
            {analysis.audience_context && (
              <div className="flex items-center gap-1">
                <Target className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600 truncate max-w-32">
                  {analysis.audience_context}
                </span>
              </div>
            )}
            
            <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getScoreBadgeColor(analysis.overall_score)}`}>
              <BarChart3 className="h-3 w-3 inline mr-1" />
              {analysis.overall_score}/100
            </div>
          </div>

          {/* Analysis Summary */}
          {analysis.advanced_analysis?.summary && (
            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
              {analysis.advanced_analysis.summary}
            </p>
          )}

          <div className="flex items-center gap-4 text-xs text-gray-500 flex-wrap">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {formatDate(analysis.created_at)}
            </div>
            {analysis.view_count > 0 && (
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                {analysis.view_count} vistas
              </div>
            )}
            {analysis.regeneration_count > 0 && (
              <div className="flex items-center gap-1">
                <RefreshCw className="h-3 w-3" />
                {analysis.regeneration_count} regeneraciones
              </div>
            )}
            <div className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              {analysis.headline_text.length} caracteres
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 ml-4">
          <Button
            size="sm"
            variant="outline"
            onClick={onToggleFavorite}
            className={analysis.is_favorite ? "text-red-600 border-red-200" : ""}
          >
            {analysis.is_favorite ? (
              <Heart className="h-4 w-4 fill-current" />
            ) : (
              <Heart className="h-4 w-4" />
            )}
          </Button>

          <Button size="sm" variant="outline" onClick={onStartRename}>
            <Edit3 className="h-4 w-4" />
          </Button>

          <Button size="sm" variant="outline" onClick={onRegenerate} className="text-blue-600 hover:text-blue-700">
            <RefreshCw className="h-4 w-4" />
          </Button>

          <Button size="sm" variant="outline" onClick={onDelete} className="text-red-600 hover:text-red-700">
            <Trash2 className="h-4 w-4" />
          </Button>

          <Button size="sm" onClick={onLoad}>
            Cargar
          </Button>
        </div>
      </div>
    </Card>
  );
}

export default HeadlineAnalysisCard;
