import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '@/hooks/use-auth'
import { headlineAnalysisService, type HeadlineAnalysis, type CreateHeadlineAnalysisData } from '@/services/headlineAnalysisService'
import { useMemo } from 'react'

/**
 * Hook for managing headline analysis data with React Query
 */
export function useHeadlineAnalysis() {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  // Query for getting user's analyses
  const {
    data: analyses = [],
    isLoading: isLoadingAnalyses,
    error: analysesError,
    refetch: refetchAnalyses
  } = useQuery({
    queryKey: ['headline-analyses', user?.id],
    queryFn: async () => {
      console.log('🔍 useHeadlineAnalysis: Fetching analyses for user:', user?.id);
      try {
        const result = await headlineAnalysisService.getUserAnalyses(user?.id || '', {
          limit: 100,
          orderBy: 'created_at',
          orderDirection: 'desc'
        });
        console.log('✅ useHeadlineAnalysis: Successfully fetched analyses:', result.length);
        return result;
      } catch (error) {
        console.error('❌ useHeadlineAnalysis: Error fetching analyses:', error);
        throw error;
      }
    },
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 30000, // 30 seconds
    gcTime: 300000, // 5 minutes
  })

  // Query for user stats
  const {
    data: userStats,
    isLoading: isLoadingStats,
    error: statsError
  } = useQuery({
    queryKey: ['headline-analysis-stats', user?.id],
    queryFn: async () => {
      console.log('📊 useHeadlineAnalysis: Fetching stats for user:', user?.id);
      try {
        const result = await headlineAnalysisService.getUserStats(user?.id || '');
        console.log('✅ useHeadlineAnalysis: Successfully fetched stats:', result);
        return result;
      } catch (error) {
        console.error('❌ useHeadlineAnalysis: Error fetching stats:', error);
        throw error;
      }
    },
    enabled: !!user?.id && user.id !== 'anonymous',
    staleTime: 60000, // 1 minute
    gcTime: 300000, // 5 minutes
  })

  // Mutation for saving analysis
  const {
    mutateAsync: saveAnalysis,
    isPending: isSaving,
    error: saveError
  } = useMutation({
    mutationFn: async (analysisData: CreateHeadlineAnalysisData) => {
      console.log('💾 useHeadlineAnalysis: Saving analysis');
      return await headlineAnalysisService.saveAnalysis(analysisData);
    },
    onSuccess: (newAnalysis) => {
      console.log('✅ useHeadlineAnalysis: Analysis saved successfully:', newAnalysis.id);
      // Invalidate and refetch analyses
      queryClient.invalidateQueries({ queryKey: ['headline-analyses', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['headline-analysis-stats', user?.id] });
    },
    onError: (error) => {
      console.error('❌ useHeadlineAnalysis: Error saving analysis:', error);
    }
  })

  // Mutation for toggling favorite
  const {
    mutateAsync: toggleFavorite,
    isPending: isTogglingFavorite,
    error: toggleFavoriteError
  } = useMutation({
    mutationFn: async (id: string) => {
      console.log('❤️ useHeadlineAnalysis: Toggling favorite for:', id);
      return await headlineAnalysisService.toggleFavorite(id);
    },
    onSuccess: (updatedAnalysis) => {
      console.log('✅ useHeadlineAnalysis: Favorite toggled successfully:', updatedAnalysis.id);
      // Update the analysis in the cache
      queryClient.setQueryData(['headline-analyses', user?.id], (oldData: HeadlineAnalysis[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map(analysis => 
          analysis.id === updatedAnalysis.id ? updatedAnalysis : analysis
        );
      });
      // Invalidate stats to update favorite count
      queryClient.invalidateQueries({ queryKey: ['headline-analysis-stats', user?.id] });
    },
    onError: (error) => {
      console.error('❌ useHeadlineAnalysis: Error toggling favorite:', error);
    }
  })

  // Mutation for deleting analysis
  const {
    mutateAsync: deleteAnalysis,
    isPending: isDeleting,
    error: deleteError
  } = useMutation({
    mutationFn: async (id: string) => {
      console.log('🗑️ useHeadlineAnalysis: Deleting analysis:', id);
      await headlineAnalysisService.deleteAnalysis(id);
      return id;
    },
    onSuccess: (deletedId) => {
      console.log('✅ useHeadlineAnalysis: Analysis deleted successfully:', deletedId);
      // Remove the analysis from the cache
      queryClient.setQueryData(['headline-analyses', user?.id], (oldData: HeadlineAnalysis[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.filter(analysis => analysis.id !== deletedId);
      });
      // Invalidate stats to update counts
      queryClient.invalidateQueries({ queryKey: ['headline-analysis-stats', user?.id] });
    },
    onError: (error) => {
      console.error('❌ useHeadlineAnalysis: Error deleting analysis:', error);
    }
  })

  // Mutation for renaming analysis
  const {
    mutateAsync: renameAnalysis,
    isPending: isRenaming,
    error: renameError
  } = useMutation({
    mutationFn: async ({ id, newName }: { id: string; newName: string }) => {
      console.log('✏️ useHeadlineAnalysis: Renaming analysis:', id, 'to:', newName);
      return await headlineAnalysisService.renameAnalysis(id, newName);
    },
    onSuccess: (updatedAnalysis) => {
      console.log('✅ useHeadlineAnalysis: Analysis renamed successfully:', updatedAnalysis.id);
      // Update the analysis in the cache
      queryClient.setQueryData(['headline-analyses', user?.id], (oldData: HeadlineAnalysis[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map(analysis => 
          analysis.id === updatedAnalysis.id ? updatedAnalysis : analysis
        );
      });
    },
    onError: (error) => {
      console.error('❌ useHeadlineAnalysis: Error renaming analysis:', error);
    }
  })

  // Memoized computed values
  const getFavoriteAnalyses = useMemo(() => {
    return () => analyses.filter(analysis => analysis.is_favorite);
  }, [analyses]);

  const getRecentAnalyses = useMemo(() => {
    return () => {
      // Get non-favorite analyses, limited to 10 most recent
      const nonFavorites = analyses.filter(analysis => !analysis.is_favorite);
      return nonFavorites.slice(0, 10);
    };
  }, [analyses]);

  // Check if user is authenticated
  const isAuthenticated = !!user && user.id !== 'anonymous';

  return {
    // Data
    analyses,
    userStats,
    
    // Loading states
    isLoadingAnalyses,
    isLoadingStats,
    
    // Error states
    analysesError,
    statsError,
    saveError,
    toggleFavoriteError,
    deleteError,
    renameError,
    
    // Mutations
    saveAnalysis,
    toggleFavorite,
    deleteAnalysis,
    renameAnalysis,
    
    // Mutation loading states
    isSaving,
    isTogglingFavorite,
    isDeleting,
    isRenaming,
    
    // Computed values
    getFavoriteAnalyses,
    getRecentAnalyses,
    
    // Utilities
    refetchAnalyses,
    isAuthenticated
  };
}
