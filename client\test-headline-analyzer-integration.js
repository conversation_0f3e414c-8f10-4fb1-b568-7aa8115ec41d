/**
 * Test script for Headline Analyzer integration with Supabase
 * Run this in the browser console on the headline analyzer page
 */

console.log('🧪 Starting Headline Analyzer Integration Test...');

// Test 1: Check if the tabs are present and functional
function testTabsPresence() {
  console.log('\n📋 Test 1: Checking tabs presence...');
  
  const tabs = document.querySelectorAll('[role="tablist"] button');
  const tabTexts = Array.from(tabs).map(tab => tab.textContent.trim());
  
  console.log('Tabs found:', tabTexts);
  
  const expectedTabs = ['Analizador de Títulos', 'Historial', 'Favoritos'];
  const hasAllTabs = expectedTabs.every(expectedTab => 
    tabTexts.some(tabText => tabText.includes(expectedTab.split(' ')[0]))
  );
  
  if (hasAllTabs) {
    console.log('✅ All tabs are present');
  } else {
    console.log('❌ Some tabs are missing');
  }
  
  return hasAllTabs;
}

// Test 2: Check if the headline analysis service is available
function testServiceAvailability() {
  console.log('\n🔧 Test 2: Checking service availability...');
  
  try {
    // Check if the service is imported and available
    const serviceExists = typeof window.headlineAnalysisService !== 'undefined';
    
    if (serviceExists) {
      console.log('✅ Headline analysis service is available');
    } else {
      console.log('⚠️ Headline analysis service not found in global scope');
      // Try to access it through React DevTools or other means
      console.log('Checking if service can be accessed through component...');
    }
    
    return serviceExists;
  } catch (error) {
    console.error('❌ Error checking service availability:', error);
    return false;
  }
}

// Test 3: Check authentication state
function testAuthenticationState() {
  console.log('\n🔐 Test 3: Checking authentication state...');
  
  try {
    // Look for auth indicators in the UI
    const authElements = document.querySelectorAll('[data-testid*="auth"], .auth, [class*="auth"]');
    const loginButtons = document.querySelectorAll('button[class*="login"], a[href*="login"]');
    const userInfo = document.querySelectorAll('[class*="user"], [data-testid*="user"]');
    
    console.log('Auth elements found:', authElements.length);
    console.log('Login buttons found:', loginButtons.length);
    console.log('User info elements found:', userInfo.length);
    
    // Check if tabs are disabled (indicating not authenticated)
    const historyTab = document.querySelector('[value="history"]');
    const favoritesTab = document.querySelector('[value="favorites"]');
    
    const historyDisabled = historyTab?.hasAttribute('disabled') || historyTab?.getAttribute('aria-disabled') === 'true';
    const favoritesDisabled = favoritesTab?.hasAttribute('disabled') || favoritesTab?.getAttribute('aria-disabled') === 'true';
    
    console.log('History tab disabled:', historyDisabled);
    console.log('Favorites tab disabled:', favoritesDisabled);
    
    if (historyDisabled && favoritesDisabled) {
      console.log('⚠️ User appears to be not authenticated (tabs disabled)');
      return false;
    } else {
      console.log('✅ User appears to be authenticated (tabs enabled)');
      return true;
    }
  } catch (error) {
    console.error('❌ Error checking authentication state:', error);
    return false;
  }
}

// Test 4: Test basic analysis functionality
function testAnalysisFunctionality() {
  console.log('\n🔍 Test 4: Testing basic analysis functionality...');
  
  try {
    // Find the headline input
    const headlineInput = document.querySelector('textarea[id="headline"], input[id="headline"]');
    const analyzeButton = document.querySelector('button:contains("Analizar"), button[class*="analiz"]');
    
    if (!headlineInput) {
      console.log('❌ Headline input not found');
      return false;
    }
    
    if (!analyzeButton) {
      console.log('❌ Analyze button not found');
      return false;
    }
    
    console.log('✅ Analysis form elements found');
    console.log('Headline input:', headlineInput);
    console.log('Analyze button:', analyzeButton);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing analysis functionality:', error);
    return false;
  }
}

// Test 5: Test tab switching
function testTabSwitching() {
  console.log('\n🔄 Test 5: Testing tab switching...');
  
  try {
    const tabs = document.querySelectorAll('[role="tablist"] button');
    
    if (tabs.length === 0) {
      console.log('❌ No tabs found');
      return false;
    }
    
    // Try to click each tab
    tabs.forEach((tab, index) => {
      try {
        console.log(`Clicking tab ${index + 1}: ${tab.textContent.trim()}`);
        tab.click();
        
        // Wait a bit for the tab content to load
        setTimeout(() => {
          const activeTab = document.querySelector('[role="tablist"] button[aria-selected="true"], [role="tablist"] button[data-state="active"]');
          if (activeTab === tab) {
            console.log(`✅ Tab ${index + 1} activated successfully`);
          } else {
            console.log(`⚠️ Tab ${index + 1} may not have activated properly`);
          }
        }, 100);
        
      } catch (error) {
        console.log(`❌ Error clicking tab ${index + 1}:`, error);
      }
    });
    
    return true;
  } catch (error) {
    console.error('❌ Error testing tab switching:', error);
    return false;
  }
}

// Test 6: Check for console errors
function checkConsoleErrors() {
  console.log('\n🐛 Test 6: Checking for console errors...');
  
  // This is a basic check - in a real scenario, you'd want to monitor console.error
  console.log('Check the browser console for any React errors, network errors, or other issues');
  console.log('Look for:');
  console.log('- Red error messages');
  console.log('- Failed network requests');
  console.log('- React component errors');
  console.log('- Supabase connection errors');
  
  return true;
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running Headline Analyzer Integration Tests...\n');
  
  const results = {
    tabsPresence: testTabsPresence(),
    serviceAvailability: testServiceAvailability(),
    authenticationState: testAuthenticationState(),
    analysisFunctionality: testAnalysisFunctionality(),
    tabSwitching: testTabSwitching(),
    consoleErrors: checkConsoleErrors()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${test}: ${status}`);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! The integration appears to be working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the individual test results above.');
  }
  
  return results;
}

// Auto-run tests when script is loaded
runAllTests();

// Make functions available globally for manual testing
window.headlineAnalyzerTests = {
  runAllTests,
  testTabsPresence,
  testServiceAvailability,
  testAuthenticationState,
  testAnalysisFunctionality,
  testTabSwitching,
  checkConsoleErrors
};
