"use client";

import { useState, useEffect, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Sparkles,
  BarChart,
  Brain,
  ThumbsUp,
  Lightbulb,
  Zap,
  Heart,
  MessagesSquare,
  MousePointerClick,
  ListTodo,
  FileBarChart,
  Search,
  PenTool,
  BarChart2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { motion, AnimatePresence } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { useHeadlineAnalysis } from "@/hooks/useHeadlineAnalysis";
import { headlineAnalysisService } from "@/services/headlineAnalysisService";
import type { HeadlineAnalysis } from "@/lib/supabase";
import HeadlineAnalysisCard from "./HeadlineAnalysisCard";

// Definición de tipos mejorada
interface HeadlineAnalysisResult {
  status: string;
  message: string;
  basic_analysis: {
    word_count: number;
    char_count: number;
    contains_number: boolean;
    is_question: boolean;
    quality_check: {
      is_valid_text: boolean;
      quality_score: number;
      issues: string[];
      is_nonsense: boolean;
    };
  };
  advanced_analysis: {
    emotional_impact: {
      score: number;
      emotions: string[];
      analysis: string;
    };
    clarity_analysis: {
      score: number;
      analysis: string;
    };
    engagement_potential: {
      score: number;
      factors: string[];
      analysis: string;
    };
    seo_analysis: {
      score: number;
      keywords: string[];
      analysis: string;
    };
    psychological_triggers: {
      triggers: string[];
      effectiveness: number;
      analysis: string;
    };
    target_audience_fit: {
      score: number;
      analysis: string;
    };
    main_topics: string[];
  };
  recommendations: {
    improvements: string[];
    alternative_headlines: string[];
    viral_variations: string[];
    platform_specific: {
      google_ads: string;
      facebook: string;
      instagram: string;
      linkedin: string;
      youtube: string;
    };
    competitive_analysis: {
      uniqueness_score: number;
      differentiation_factors: string[];
      market_positioning: string;
    };
    strengths: string[];
    weaknesses: string[];
  };
  overall_score: number;
  processing_time: number;
}

interface ApiErrorResponse {
  error: string;
  message: string;
  request_id?: string;
}

// Componente auxiliar para mostrar una métrica con un icono
interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  description?: string;
}

function MetricCard({
  title,
  value,
  icon,
  color,
  description,
}: MetricCardProps) {
  return (
    <Card className="border-2 border-gray-200 shadow-sm hover:shadow-md transition-all">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-1">
              {title}
            </p>
            <p className={`text-2xl font-black ${color}`}>{value}</p>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">
                {description}
              </p>
            )}
          </div>
          <div
            className={`p-2 rounded-full ${color.replace("text-", "bg-").replace("-600", "-100")}`}
          >
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Tipos de contenido disponibles
const CONTENT_TYPES = [
  { value: "blog", label: "📝 Artículo de Blog", description: "Títulos para posts y artículos" },
  { value: "facebook_ad", label: "📘 Anuncio Facebook", description: "Títulos para ads de Facebook" },
  { value: "instagram_ad", label: "📸 Anuncio Instagram", description: "Títulos para ads de Instagram" },
  { value: "google_ad", label: "🔍 Anuncio Google", description: "Títulos para Google Ads" },
  { value: "email", label: "📧 Email Marketing", description: "Asuntos de email" },
  { value: "youtube", label: "🎥 Video YouTube", description: "Títulos para videos" },
  { value: "landing_page", label: "🎯 Landing Page", description: "Títulos principales de páginas" },
  { value: "social_post", label: "📱 Post Social", description: "Títulos para redes sociales" },
  { value: "product", label: "🛍️ Producto", description: "Nombres y títulos de productos" },
  { value: "newsletter", label: "📰 Newsletter", description: "Títulos de boletines" }
] as const;

// Audiencias objetivo predefinidas
const TARGET_AUDIENCES = [
  { value: "emprendedores", label: "👔 Emprendedores" },
  { value: "marketing_digital", label: "📊 Marketing Digital" },
  { value: "ecommerce", label: "🛒 E-commerce" },
  { value: "freelancers", label: "💼 Freelancers" },
  { value: "startups", label: "🚀 Startups" },
  { value: "pymes", label: "🏢 PyMEs" },
  { value: "estudiantes", label: "🎓 Estudiantes" },
  { value: "profesionales", label: "👨‍💼 Profesionales" },
  { value: "padres", label: "👨‍👩‍👧‍👦 Padres de Familia" },
  { value: "jovenes", label: "🧑‍🎤 Jóvenes (18-30)" },
  { value: "adultos", label: "👩‍🦳 Adultos (30-50)" },
  { value: "seniors", label: "👴 Seniors (50+)" },
  { value: "tecnologia", label: "💻 Tecnología" },
  { value: "salud", label: "🏥 Salud y Bienestar" },
  { value: "finanzas", label: "💰 Finanzas" },
  { value: "educacion", label: "📚 Educación" },
  { value: "fitness", label: "💪 Fitness" },
  { value: "viajes", label: "✈️ Viajes" },
  { value: "comida", label: "🍕 Comida y Restaurantes" },
  { value: "moda", label: "👗 Moda y Belleza" }
] as const;

// Old localStorage functions removed - now using Supabase database

export default function HeadlineAnalyzer() {
  const [headline, setHeadline] = useState("");
  const [contentType, setContentType] = useState("blog");
  const [targetAudience, setTargetAudience] = useState("");
  const [customAudience, setCustomAudience] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStage, setLoadingStage] = useState(0);
  const [results, setResults] = useState<HeadlineAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("analyze");
  const { toast } = useToast();

  // Authentication and analysis hooks
  const { user } = useAuth();
  const {
    analyses,
    isLoadingAnalyses,
    analysesError,
    saveAnalysis,
    isSaving,
    toggleFavorite,
    isTogglingFavorite,
    deleteAnalysis,
    isDeleting,
    getFavoriteAnalyses,
    getRecentAnalyses,
    userStats,
    refetchAnalyses,
    isAuthenticated
  } = useHeadlineAnalysis();

  // Get computed values
  const favoriteAnalyses = getFavoriteAnalyses();
  const recentAnalyses = getRecentAnalyses();

  // State for card interactions
  const [renamingAnalysisId, setRenamingAnalysisId] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState("");

  // Mensajes para la animación de carga (más detallados para mostrar el análisis real con Emma AI)
  const loadingMessages = [
    "Escaneando el título...",
    "Analizando estructura y palabras clave...",
    "Enviando contenido a Emma AI...",
    "Calculando métricas de impacto emocional...",
    "Evaluando claridad y potencial de engagement...",
    "Analizando factores de conversión (CTR)...",
    "Aplicando análisis de tendencias actuales...",
    "Procesando respuesta de Emma AI...",
    "Generando recomendaciones personalizadas...",
    "Finalizando análisis exhaustivo...",
  ];

  // Estados adicionales para el tiempo de carga
  const [loadingTime, setLoadingTime] = useState(0);
  const MAX_LOADING_TIME = 30; // segundos máximos de espera (igual que en backend)

  // Usar refs para almacenar las referencias a los intervalos
  // Esto evita problemas con el ciclo de vida y previene memory leaks
  const stageIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Efecto para avanzar en las etapas de carga cuando isLoading es true
  useEffect(() => {
    // Función para limpiar todos los intervalos
    const clearAllIntervals = () => {
      if (stageIntervalRef.current) {
        clearInterval(stageIntervalRef.current);
        stageIntervalRef.current = null;
      }
      if (timeIntervalRef.current) {
        clearInterval(timeIntervalRef.current);
        timeIntervalRef.current = null;
      }
    };

    // Limpiar intervalos existentes antes de crear nuevos
    clearAllIntervals();

    if (isLoading) {
      // Reiniciar estados
      setLoadingStage(0);
      setLoadingTime(0);

      // Intervalo para avanzar las etapas de carga
      stageIntervalRef.current = setInterval(() => {
        setLoadingStage((prev) => {
          // Si estamos en la última etapa, quedarse ahí hasta que termine
          if (prev >= loadingMessages.length - 1) return prev;
          return prev + 1;
        });
      }, 1500); // Avanza cada 1.5 segundos

      // Intervalo para contar el tiempo de carga
      timeIntervalRef.current = setInterval(() => {
        setLoadingTime((prev) => {
          const newTime = prev + 1;
          // Si superamos el tiempo máximo, mostrar mensaje de advertencia
          if (newTime === MAX_LOADING_TIME) {
            setError(
              "La respuesta está tardando más de lo habitual. Estamos utilizando IA avanzada para analizar su titular, lo que puede tomar tiempo. Por favor, sea paciente.",
            );
          }
          return newTime;
        });
      }, 1000); // Contar cada segundo
    }

    // Función de limpieza que se ejecuta cuando el componente se desmonta
    // o cuando las dependencias cambian
    return clearAllIntervals;
  }, [isLoading, loadingMessages.length, MAX_LOADING_TIME]);

  // Función para validar el título antes del análisis
  const validateHeadline = (text: string): { isValid: boolean; error?: string } => {
    const trimmed = text.trim();

    if (!trimmed) {
      return { isValid: false, error: "Por favor, ingresa un título para analizar" };
    }

    if (trimmed.length < 5) {
      return { isValid: false, error: "El título debe tener al menos 5 caracteres" };
    }

    if (trimmed.length > 500) {
      return { isValid: false, error: "El título no puede exceder 500 caracteres" };
    }

    const words = trimmed.split(/\s+/).filter(w => w.length > 0);
    if (words.length < 2) {
      return { isValid: false, error: "El título debe contener al menos 2 palabras" };
    }

    // Verificar si contiene solo caracteres especiales o números
    if (!/[a-zA-ZáéíóúÁÉÍÓÚñÑ]/.test(trimmed)) {
      return { isValid: false, error: "El título debe contener texto significativo" };
    }

    return { isValid: true };
  };

  // Función para analizar el título
  const analyzeHeadline = async () => {
    // Validación mejorada
    const validation = validateHeadline(headline);
    if (!validation.isValid) {
      toast({
        title: "Título inválido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    // Reiniciar estados
    setIsLoading(true);
    setLoadingStage(0);
    setLoadingTime(0);
    setError(null);
    setResults(null);

    try {
      // Preparar contexto de audiencia
      const finalAudienceContext = customAudience.trim() ||
        (targetAudience && targetAudience !== "none" ? TARGET_AUDIENCES.find(a => a.value === targetAudience)?.label : '') ||
        '';

      // Preparar contexto de tipo de contenido
      const contentTypeContext = CONTENT_TYPES.find(ct => ct.value === contentType)?.description || '';

      // Datos para enviar
      const requestData = {
        headline: headline.trim(),
        audience_context: finalAudienceContext,
        content_type: contentType,
        content_type_context: contentTypeContext,
      };

      // Llamada a la API con timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 45000); // 45 segundos timeout

      const response = await fetch("/api/analyze-headline", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Request-ID": `headline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        },
        body: JSON.stringify(requestData),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = "Error al analizar el título";
        try {
          const errorData: ApiErrorResponse = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          // Si no se puede parsear el JSON, usar mensaje genérico
          errorMessage = `Error del servidor (${response.status})`;
        }
        throw new Error(errorMessage);
      }

      const data: HeadlineAnalysisResult = await response.json();
      setResults(data);

      // Save analysis to database if user is authenticated
      if (isAuthenticated && user?.id) {
        try {
          const analysisData = {
            user_id: user.id,
            headline_text: headline.trim(),
            content_type: contentType,
            audience_context: finalAudienceContext,
            overall_score: data.overall_score,
            basic_analysis: data.basic_analysis,
            advanced_analysis: data.advanced_analysis,
            recommendations: data.recommendations,
            analysis_duration_ms: Math.round((data.processing_time || 0) * 1000),
            status: 'completed' as const
          };

          await saveAnalysis(analysisData);
          console.log('✅ Analysis saved to database successfully');
        } catch (saveError) {
          console.error('❌ Failed to save analysis to database:', saveError);
          // Don't show error to user - analysis still worked
        }
      }

      toast({
        title: "Análisis completado",
        description: `Análisis procesado en ${data.processing_time?.toFixed(2) || 'N/A'} segundos`,
        variant: "default",
      });

    } catch (err) {
      console.error("Error en análisis de título:", err);

      let errorMessage = "Ocurrió un error al analizar el título";

      if (err instanceof Error) {
        if (err.name === 'AbortError') {
          errorMessage = "El análisis tardó demasiado tiempo. Por favor, intenta con un título más corto.";
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      toast({
        title: "Error en el análisis",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Función para obtener el color según la puntuación
  const getScoreColor = (score: number): string => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    if (score >= 40) return "text-orange-600";
    return "text-red-600";
  };

  // Función para obtener descripción de la puntuación
  const getScoreDescription = (score: number): string => {
    if (score >= 80) return "Excelente";
    if (score >= 60) return "Bueno";
    if (score >= 40) return "Regular";
    return "Necesita mejoras";
  };

  // Card interaction handlers
  const handleLoadAnalysis = (analysis: HeadlineAnalysis) => {
    setHeadline(analysis.headline_text);
    setContentType(analysis.content_type);
    setCustomAudience(analysis.audience_context || '');
    setResults({
      status: analysis.status,
      message: 'Analysis loaded from history',
      basic_analysis: analysis.basic_analysis,
      advanced_analysis: analysis.advanced_analysis,
      recommendations: analysis.recommendations,
      overall_score: analysis.overall_score,
      processing_time: (analysis.analysis_duration_ms || 0) / 1000
    });
    setActiveTab('analyze');

    // Record view
    headlineAnalysisService.recordView(analysis.id);
  };

  const handleToggleFavorite = async (analysis: HeadlineAnalysis) => {
    try {
      await toggleFavorite(analysis.id);
      toast({
        title: analysis.is_favorite ? "Eliminado de favoritos" : "Añadido a favoritos",
        description: analysis.is_favorite ?
          "El análisis se eliminó de tus favoritos" :
          "El análisis se guardó en tus favoritos",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo actualizar el estado de favorito",
        variant: "destructive",
      });
    }
  };

  const handleRename = async (analysis: HeadlineAnalysis, newName: string) => {
    try {
      await headlineAnalysisService.renameAnalysis(analysis.id, newName);
      setRenamingAnalysisId(null);
      setRenameValue("");
      toast({
        title: "Análisis renombrado",
        description: "El nombre del análisis se actualizó correctamente",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo renombrar el análisis",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (analysis: HeadlineAnalysis) => {
    try {
      await deleteAnalysis(analysis.id);
      toast({
        title: "Análisis eliminado",
        description: "El análisis se eliminó correctamente",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo eliminar el análisis",
        variant: "destructive",
      });
    }
  };

  const handleRegenerate = (analysis: HeadlineAnalysis) => {
    setHeadline(analysis.headline_text);
    setContentType(analysis.content_type);
    setCustomAudience(analysis.audience_context || '');
    setActiveTab('analyze');
    // Trigger analysis
    setTimeout(() => analyzeHeadline(), 100);
  };

  const handleStartRename = (analysis: HeadlineAnalysis) => {
    setRenamingAnalysisId(analysis.id);
    setRenameValue(analysis.custom_name || `Análisis ${new Date(analysis.created_at).toLocaleDateString()}`);
  };

  const handleCancelRename = () => {
    setRenamingAnalysisId(null);
    setRenameValue("");
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="analyze">Analizador de Títulos</TabsTrigger>
          <TabsTrigger value="history" disabled={!isAuthenticated && process.env.NODE_ENV !== 'development'} className="flex items-center gap-2">
            <History className="h-4 w-4" />
            Historial ({recentAnalyses.length})
          </TabsTrigger>
          <TabsTrigger value="favorites" disabled={!isAuthenticated && process.env.NODE_ENV !== 'development'} className="flex items-center gap-2">
            <Heart className="h-4 w-4" />
            Favoritos ({favoriteAnalyses.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="analyze" className="space-y-6">
          <div className="flex flex-col md:flex-row items-start gap-6">
            {/* Panel de entrada */}
            <Card className="w-full md:w-2/5 border-2 border-gray-200 shadow-md hover:shadow-lg transition-all">
          <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
            <CardTitle className="text-blue-600 flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-600" />
              Analizador de Títulos
            </CardTitle>
            <CardDescription>
              Optimiza tus títulos para máximo impacto y conversión
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="headline">Título a analizar</Label>
              <Textarea
                id="headline"
                placeholder="Ingresa el título que deseas analizar"
                value={headline}
                onChange={(e) => setHeadline(e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-xs text-muted-foreground">
                Para mejores resultados, ingresa un título completo de artículo,
                email o anuncio.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="content-type">Tipo de contenido</Label>
              <Select value={contentType} onValueChange={setContentType}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona el tipo de contenido" />
                </SelectTrigger>
                <SelectContent>
                  {CONTENT_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex flex-col">
                        <span>{type.label}</span>
                        <span className="text-xs text-muted-foreground">{type.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Esto optimizará el análisis para el tipo específico de contenido.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="audience">Audiencia objetivo</Label>
              <Select value={targetAudience} onValueChange={setTargetAudience}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona tu audiencia (opcional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Sin especificar</SelectItem>
                  {TARGET_AUDIENCES.map((audience) => (
                    <SelectItem key={audience.value} value={audience.value}>
                      {audience.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                O especifica una audiencia personalizada abajo.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="custom-audience">Audiencia personalizada (opcional)</Label>
              <Input
                id="custom-audience"
                placeholder="Ej: Desarrolladores de React, Madres primerizas, etc."
                value={customAudience}
                onChange={(e) => setCustomAudience(e.target.value)}
                disabled={targetAudience !== "" && targetAudience !== "none"}
              />
              <p className="text-xs text-muted-foreground">
                {targetAudience && targetAudience !== "none"
                  ? "Deselecciona la audiencia predefinida para usar una personalizada"
                  : "Describe tu audiencia específica para un análisis más preciso"
                }
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={analyzeHeadline} // Usando la API real de Gemini
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold"
            >
              {isLoading ? "Analizando..." : "Analizar título"}
            </Button>
          </CardFooter>
        </Card>

        {/* Panel de resultados */}
        <Card className="w-full md:w-3/5 border-2 border-gray-200 shadow-md hover:shadow-lg transition-all">
          {isLoading ? (
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center space-y-6 py-12">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={loadingStage}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                    className="flex items-center gap-3"
                  >
                    {loadingStage === 0 && (
                      <Search className="h-6 w-6 text-blue-500 animate-pulse" />
                    )}
                    {loadingStage === 1 && (
                      <PenTool className="h-6 w-6 text-indigo-500 animate-pulse" />
                    )}
                    {loadingStage === 2 && (
                      <Brain className="h-6 w-6 text-purple-500 animate-pulse" />
                    )}
                    {loadingStage === 3 && (
                      <Heart className="h-6 w-6 text-pink-500 animate-pulse" />
                    )}
                    {loadingStage === 4 && (
                      <Target className="h-6 w-6 text-teal-500 animate-pulse" />
                    )}
                    {loadingStage === 5 && (
                      <BarChart2 className="h-6 w-6 text-yellow-500 animate-pulse" />
                    )}
                    {loadingStage === 6 && (
                      <MessagesSquare className="h-6 w-6 text-blue-500 animate-pulse" />
                    )}
                    {loadingStage === 7 && (
                      <FileBarChart className="h-6 w-6 text-violet-500 animate-pulse" />
                    )}
                    {loadingStage === 8 && (
                      <Lightbulb className="h-6 w-6 text-orange-500 animate-pulse" />
                    )}
                    {loadingStage === 9 && (
                      <BadgeCheck className="h-6 w-6 text-green-500 animate-pulse" />
                    )}
                    <h3 className="text-lg font-medium">
                      {loadingMessages[loadingStage]}
                    </h3>
                  </motion.div>
                </AnimatePresence>

                <motion.div
                  className="w-2/3"
                  animate={{
                    scale: [1, 1.02, 1],
                    transition: { duration: 2, repeat: Infinity },
                  }}
                >
                  <Progress
                    value={Math.min(10 + loadingStage * 9, 95)}
                    className="h-2 bg-gray-100"
                  />
                </motion.div>

                {loadingTime >= 10 && (
                  <Alert
                    variant="warning"
                    className="mt-4 w-full max-w-md animate-pulse bg-blue-50 border-blue-200"
                  >
                    <Clock className="h-4 w-4" />
                    <AlertTitle>Análisis en progreso</AlertTitle>
                    <AlertDescription className="text-sm">
                      El análisis está tomando un poco más de lo habitual.
                      Estamos utilizando IA avanzada con Emma AI para analizar su
                      titular de forma exhaustiva.
                    </AlertDescription>
                  </Alert>
                )}

                <motion.p
                  className="text-sm text-muted-foreground text-center max-w-md"
                  animate={{
                    opacity: [0.7, 1, 0.7],
                    transition: { duration: 2, repeat: Infinity },
                  }}
                >
                  Nuestro modelo de IA está evaluando múltiples factores para
                  brindarte el mejor análisis posible
                  {loadingTime > 0 && (
                    <span className="block mt-1 text-xs text-blue-600">
                      Tiempo transcurrido: {loadingTime} segundos
                    </span>
                  )}
                </motion.p>
              </div>
            </CardContent>
          ) : error ? (
            <CardContent className="pt-6">
              <Alert
                variant={error.includes("tardando") ? "warning" : "destructive"}
                className={
                  error.includes("tardando")
                    ? "bg-yellow-50 border-yellow-200"
                    : ""
                }
              >
                {error.includes("tardando") ? (
                  <Clock className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <AlertTitle>
                  {error.includes("tardando")
                    ? "Procesamiento extenso"
                    : "Error"}
                </AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>

              {error.includes("tardando") && (
                <div className="mt-4 flex justify-center">
                  <Button
                    onClick={analyzeHeadline}
                    variant="outline"
                    className="mr-2"
                  >
                    Intentar nuevamente
                  </Button>
                  <Button
                    onClick={() => {
                      setHeadline(headline.split(" ").slice(0, 5).join(" "));
                      setError(null);
                    }}
                    variant="default"
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Simplificar titular
                  </Button>
                </div>
              )}
            </CardContent>
          ) : results && results.status === "warning" ? (
            <>
              <CardHeader className="border-b border-gray-100 bg-yellow-50 rounded-t-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-yellow-600 flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                      Advertencia: Texto posiblemente sin sentido
                    </CardTitle>
                    <CardDescription className="text-yellow-700">
                      {results.message ||
                        "El texto ingresado podría contener elementos sin sentido o aleatorios"}
                    </CardDescription>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800 px-3 py-1 text-sm">
                    CTR: Muy Bajo
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <Alert variant="warning" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Problemas detectados</AlertTitle>
                  <AlertDescription>
                    <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                      {results.issues &&
                        results.issues.map((issue, i) => (
                          <li key={i}>{issue}</li>
                        ))}
                    </ul>
                  </AlertDescription>
                </Alert>

                <div className="p-4 border rounded-md bg-gray-50 mb-4">
                  <p className="font-medium mb-1">Título analizado:</p>
                  <p className="text-lg">{headline}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <MetricCard
                    title="Potencial de clics (CTR)"
                    value={`${results.advanced_analysis?.engagement_prediction?.predicted_ctr_score || 10}%`}
                    color="text-red-600"
                    icon={<Target className="h-5 w-5" />}
                    description="Probabilidad muy baja de obtener clics"
                  />

                  <MetricCard
                    title="Claridad"
                    value={`${results.advanced_analysis?.clarity_analysis?.score || 10}%`}
                    color="text-red-600"
                    icon={<Brain className="h-5 w-5" />}
                    description="Problemas significativos de claridad"
                  />
                </div>

                <div className="mt-6 p-4 border rounded-md bg-blue-50">
                  <h3 className="text-lg font-medium text-blue-700 mb-2">
                    Recomendación
                  </h3>
                  <p className="text-blue-600">
                    Intenta reformular el título para que tenga un mensaje claro
                    y coherente. Evita secuencias aleatorias de caracteres y
                    asegúrate de que comunique un beneficio o mensaje
                    específico.
                  </p>
                </div>
              </CardContent>
            </>
          ) : results && (results.status === "success" || true) ? (
            <>
              <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-blue-600 flex items-center gap-2">
                      <BarChart className="h-5 w-5 text-blue-600" />
                      Resultado del análisis
                    </CardTitle>
                    <CardDescription>
                      Análisis completo de impacto y efectividad
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      className={`px-3 py-1 text-sm ${
                        results.overall_score >= 80
                          ? "bg-green-100 text-green-800"
                          : results.overall_score >= 60
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800"
                      }`}
                    >
                      Puntuación: {results.overall_score}/100
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <Tabs defaultValue="resumen">
                  <TabsList className="grid grid-cols-5 mb-6 bg-gray-100">
                    <TabsTrigger
                      value="resumen"
                      className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
                    >
                      Resumen
                    </TabsTrigger>
                    <TabsTrigger
                      value="analisis"
                      className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
                    >
                      Análisis
                    </TabsTrigger>
                    <TabsTrigger
                      value="mejoras"
                      className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
                    >
                      Mejoras
                    </TabsTrigger>
                    <TabsTrigger
                      value="variaciones"
                      className="data-[state=active]:bg-red-600 data-[state=active]:text-white"
                    >
                      🔥 Variaciones
                    </TabsTrigger>
                    <TabsTrigger
                      value="competencia"
                      className="data-[state=active]:bg-purple-600 data-[state=active]:text-white"
                    >
                      📊 Competencia
                    </TabsTrigger>
                  </TabsList>

                  {/* Pestaña Resumen */}
                  <TabsContent value="resumen" className="space-y-4">
                    <div className="p-4 border rounded-md mb-4">
                      <p className="font-medium mb-1">Título analizado:</p>
                      <p className="text-lg">{headline}</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <MetricCard
                        title="Puntuación General"
                        value={`${results.overall_score}%`}
                        color={getScoreColor(results.overall_score)}
                        icon={<BarChart className="h-5 w-5" />}
                        description={getScoreDescription(results.overall_score)}
                      />

                      <MetricCard
                        title="Potencial de Engagement"
                        value={`${results.advanced_analysis.engagement_potential.score}%`}
                        color={getScoreColor(results.advanced_analysis.engagement_potential.score)}
                        icon={<Target className="h-5 w-5" />}
                        description={getScoreDescription(results.advanced_analysis.engagement_potential.score)}
                      />

                      <MetricCard
                        title="Claridad"
                        value={`${results.advanced_analysis.clarity_analysis.score}%`}
                        color={getScoreColor(results.advanced_analysis.clarity_analysis.score)}
                        icon={<Brain className="h-5 w-5" />}
                        description="Nivel de comprensión del mensaje"
                      />

                      <MetricCard
                        title="Impacto Emocional"
                        value={`${results.advanced_analysis.emotional_impact.score}%`}
                        color={getScoreColor(results.advanced_analysis.emotional_impact.score)}
                        icon={<Heart className="h-5 w-5" />}
                        description="Conexión emocional con la audiencia"
                      />

                      {results.basic_analysis && (
                        <>
                          <MetricCard
                            title="Longitud"
                            value={results.basic_analysis.char_count}
                            color="text-blue-600"
                            icon={<Zap className="h-5 w-5" />}
                            description={`${results.basic_analysis.word_count} palabras, ${results.basic_analysis.char_count} caracteres`}
                          />

                          <MetricCard
                            title="Tipo"
                            value={
                              results.basic_analysis.is_question
                                ? "Pregunta"
                                : "Afirmación"
                            }
                            color="text-purple-600"
                            icon={
                              results.basic_analysis.is_question ? (
                                <Sparkles className="h-5 w-5" />
                              ) : (
                                <CheckCircle className="h-5 w-5" />
                              )
                            }
                            description={
                              results.basic_analysis.contains_number
                                ? "Contiene números"
                                : "Sin números"
                            }
                          />
                        </>
                      )}
                    </div>

                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                      <CardHeader className="pb-2 bg-gray-50">
                        <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                          <MessagesSquare className="h-4 w-4 text-blue-600" />
                          Análisis SEO y Palabras Clave
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="mb-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Puntuación SEO</span>
                            <span className={`text-sm font-bold ${getScoreColor(results.advanced_analysis.seo_analysis.score)}`}>
                              {results.advanced_analysis.seo_analysis.score}%
                            </span>
                          </div>
                          <Progress value={results.advanced_analysis.seo_analysis.score} className="h-2" />
                        </div>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {results.advanced_analysis.seo_analysis.keywords.map((keyword, index) => (
                            <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-800">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {results.advanced_analysis.seo_analysis.analysis}
                        </p>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Pestaña Análisis */}
                  <TabsContent value="analisis" className="space-y-4">
                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                      <CardHeader className="pb-2 bg-gray-50">
                        <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                          <Heart className="h-4 w-4 text-blue-600" />
                          Análisis de Impacto Emocional
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="mb-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Puntuación Emocional</span>
                            <span className={`text-sm font-bold ${getScoreColor(results.advanced_analysis.emotional_impact.score)}`}>
                              {results.advanced_analysis.emotional_impact.score}%
                            </span>
                          </div>
                          <Progress value={results.advanced_analysis.emotional_impact.score} className="h-2" />
                        </div>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {results.advanced_analysis.emotional_impact.emotions.map((emotion, index) => (
                            <Badge key={index} variant="secondary" className="bg-pink-100 text-pink-800">
                              {emotion}
                            </Badge>
                          ))}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {results.advanced_analysis.emotional_impact.analysis}
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                      <CardHeader className="pb-2 bg-gray-50">
                        <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                          <MousePointerClick className="h-4 w-4 text-blue-600" />
                          Análisis de Engagement
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="mb-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Potencial de Engagement</span>
                            <span className={`text-sm font-bold ${getScoreColor(results.advanced_analysis.engagement_potential.score)}`}>
                              {results.advanced_analysis.engagement_potential.score}%
                            </span>
                          </div>
                          <Progress value={results.advanced_analysis.engagement_potential.score} className="h-2" />
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {results.advanced_analysis.engagement_potential.analysis}
                        </p>
                        <div>
                          <h4 className="text-sm font-medium mb-2">Factores de Engagement</h4>
                          <div className="flex flex-wrap gap-2">
                            {results.advanced_analysis.engagement_potential.factors.map((factor, index) => (
                              <Badge key={index} variant="outline" className="font-normal">
                                {factor}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                      <CardHeader className="pb-2 bg-gray-50">
                        <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                          <Brain className="h-4 w-4 text-blue-600" />
                          Triggers Psicológicos
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="mb-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Efectividad</span>
                            <span className={`text-sm font-bold ${getScoreColor(results.advanced_analysis.psychological_triggers.effectiveness)}`}>
                              {results.advanced_analysis.psychological_triggers.effectiveness}%
                            </span>
                          </div>
                          <Progress value={results.advanced_analysis.psychological_triggers.effectiveness} className="h-2" />
                        </div>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {results.advanced_analysis.psychological_triggers.triggers.map((trigger, index) => (
                            <Badge key={index} variant="secondary" className="bg-purple-100 text-purple-800">
                              {trigger}
                            </Badge>
                          ))}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {results.advanced_analysis.psychological_triggers.analysis}
                        </p>
                      </CardContent>
                    </Card>

                    {results.advanced_analysis?.main_topics &&
                      results.advanced_analysis.main_topics.length > 0 && (
                        <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                          <CardHeader className="pb-2 bg-gray-50">
                            <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                              <ListTodo className="h-4 w-4 text-blue-600" />
                              Temas principales
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="flex flex-wrap gap-2">
                              {results.advanced_analysis.main_topics.map(
                                (topic, index) => (
                                  <Badge key={index} variant="secondary">
                                    {topic}
                                  </Badge>
                                ),
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      )}
                  </TabsContent>

                  {/* Pestaña Mejoras */}
                  <TabsContent value="mejoras" className="space-y-4">
                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                      <CardHeader className="pb-2 bg-gray-50">
                        <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                          <ThumbsUp className="h-4 w-4 text-blue-600" />
                          Fortalezas
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {results.recommendations.strengths.map((strength, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <ThumbsUp className="h-4 w-4 text-green-500 mt-0.5" />
                              <span className="text-sm">{strength}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                      <CardHeader className="pb-2 bg-gray-50">
                        <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-blue-600" />
                          Áreas de mejora
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {results.recommendations.weaknesses.map((weakness, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
                              <span className="text-sm">{weakness}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                      <CardHeader className="pb-2 bg-gray-50">
                        <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                          <Lightbulb className="h-4 w-4 text-blue-600" />
                          Sugerencias de mejora
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-3">
                          {results.recommendations.improvements.map((improvement, index) => (
                            <li key={index} className="p-2 border rounded-md">
                              <div className="flex items-start gap-2">
                                <Lightbulb className="h-4 w-4 text-blue-500 mt-0.5" />
                                <span className="text-sm">{improvement}</span>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                      <CardHeader className="pb-2 bg-gray-50">
                        <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                          <Sparkles className="h-4 w-4 text-blue-600" />
                          Títulos alternativos
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-3">
                          {results.recommendations.alternative_headlines.map((alternative, index) => (
                            <li key={index} className="p-3 border rounded-md bg-blue-50">
                              <div className="flex items-start gap-2">
                                <Sparkles className="h-4 w-4 text-blue-500 mt-0.5" />
                                <span className="text-sm font-medium">{alternative}</span>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Pestaña Variaciones 🔥 */}
                  <TabsContent value="variaciones" className="space-y-4">
                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-red-100">
                      <CardHeader className="pb-2 bg-red-50">
                        <CardTitle className="text-base text-red-600 flex items-center gap-2">
                          <Sparkles className="h-4 w-4 text-red-600" />
                          🔥 Títulos Alternativos
                        </CardTitle>
                        <CardDescription>
                          5 variaciones creativas de tu título original
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-3">
                          {results.recommendations.alternative_headlines.map((alternative, index) => (
                            <li key={index} className="p-3 border rounded-md bg-gradient-to-r from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100 transition-all cursor-pointer">
                              <div className="flex items-start gap-3">
                                <Badge variant="outline" className="bg-white text-blue-600 border-blue-300">
                                  #{index + 1}
                                </Badge>
                                <span className="text-sm font-medium flex-1">{alternative}</span>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0 hover:bg-blue-200"
                                  onClick={() => {
                                    navigator.clipboard.writeText(alternative);
                                    toast({
                                      title: "¡Copiado!",
                                      description: "Título copiado al portapapeles",
                                      variant: "default",
                                    });
                                  }}
                                >
                                  📋
                                </Button>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-orange-100">
                      <CardHeader className="pb-2 bg-orange-50">
                        <CardTitle className="text-base text-orange-600 flex items-center gap-2">
                          <Zap className="h-4 w-4 text-orange-600" />
                          🎯 Variaciones VIRALES
                        </CardTitle>
                        <CardDescription>
                          Títulos optimizados para máximo engagement y viralidad
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-3">
                          {results.recommendations.viral_variations.map((viral, index) => (
                            <li key={index} className="p-3 border rounded-md bg-gradient-to-r from-orange-50 to-red-50 hover:from-orange-100 hover:to-red-100 transition-all cursor-pointer">
                              <div className="flex items-start gap-3">
                                <Badge variant="outline" className="bg-white text-orange-600 border-orange-300">
                                  🔥 {index + 1}
                                </Badge>
                                <span className="text-sm font-medium flex-1">{viral}</span>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0 hover:bg-orange-200"
                                  onClick={() => {
                                    navigator.clipboard.writeText(viral);
                                    toast({
                                      title: "¡Copiado!",
                                      description: "Título viral copiado al portapapeles",
                                      variant: "default",
                                    });
                                  }}
                                >
                                  📋
                                </Button>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-green-100">
                      <CardHeader className="pb-2 bg-green-50">
                        <CardTitle className="text-base text-green-600 flex items-center gap-2">
                          <Target className="h-4 w-4 text-green-600" />
                          📱 Optimizados por Plataforma
                        </CardTitle>
                        <CardDescription>
                          Títulos adaptados para cada red social y plataforma
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="p-3 border rounded-md bg-blue-50">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className="bg-blue-600 text-white">🔍 Google Ads</Badge>
                            </div>
                            <p className="text-sm font-medium">{results.recommendations.platform_specific.google_ads}</p>
                          </div>

                          <div className="p-3 border rounded-md bg-blue-50">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className="bg-blue-800 text-white">📘 Facebook</Badge>
                            </div>
                            <p className="text-sm font-medium">{results.recommendations.platform_specific.facebook}</p>
                          </div>

                          <div className="p-3 border rounded-md bg-pink-50">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className="bg-pink-600 text-white">📸 Instagram</Badge>
                            </div>
                            <p className="text-sm font-medium">{results.recommendations.platform_specific.instagram}</p>
                          </div>

                          <div className="p-3 border rounded-md bg-blue-50">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className="bg-blue-700 text-white">💼 LinkedIn</Badge>
                            </div>
                            <p className="text-sm font-medium">{results.recommendations.platform_specific.linkedin}</p>
                          </div>

                          <div className="p-3 border rounded-md bg-red-50">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className="bg-red-600 text-white">🎥 YouTube</Badge>
                            </div>
                            <p className="text-sm font-medium">{results.recommendations.platform_specific.youtube}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Pestaña Competencia 📊 */}
                  <TabsContent value="competencia" className="space-y-4">
                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-purple-100">
                      <CardHeader className="pb-2 bg-purple-50">
                        <CardTitle className="text-base text-purple-600 flex items-center gap-2">
                          <BarChart className="h-4 w-4 text-purple-600" />
                          📊 Análisis Competitivo
                        </CardTitle>
                        <CardDescription>
                          Cómo se posiciona tu título frente a la competencia
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="p-4 border rounded-md bg-gradient-to-r from-purple-50 to-blue-50">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium">Puntuación de Unicidad</span>
                              <span className={`text-lg font-bold ${getScoreColor(results.recommendations.competitive_analysis.uniqueness_score)}`}>
                                {results.recommendations.competitive_analysis.uniqueness_score}%
                              </span>
                            </div>
                            <Progress value={results.recommendations.competitive_analysis.uniqueness_score} className="h-3" />
                            <p className="text-xs text-muted-foreground mt-2">
                              Qué tan único es tu título comparado con títulos similares en el mercado
                            </p>
                          </div>

                          <div className="p-4 border rounded-md bg-green-50">
                            <h4 className="text-sm font-medium mb-3 text-green-700">🎯 Factores de Diferenciación</h4>
                            <ul className="space-y-2">
                              {results.recommendations.competitive_analysis.differentiation_factors.map((factor, index) => (
                                <li key={index} className="flex items-start gap-2">
                                  <Badge variant="outline" className="bg-white text-green-600 border-green-300 text-xs">
                                    ✓
                                  </Badge>
                                  <span className="text-sm">{factor}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div className="p-4 border rounded-md bg-blue-50">
                            <h4 className="text-sm font-medium mb-2 text-blue-700">🏆 Posicionamiento en el Mercado</h4>
                            <p className="text-sm text-blue-600">
                              {results.recommendations.competitive_analysis.market_positioning}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="shadow-sm hover:shadow-md transition-all border-2 border-yellow-100">
                      <CardHeader className="pb-2 bg-yellow-50">
                        <CardTitle className="text-base text-yellow-600 flex items-center gap-2">
                          <Lightbulb className="h-4 w-4 text-yellow-600" />
                          💡 Estrategia Competitiva
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="p-3 border rounded-md bg-yellow-50">
                            <h5 className="text-sm font-medium mb-2">🎯 Recomendación Estratégica</h5>
                            <p className="text-sm">
                              {results.recommendations.competitive_analysis.uniqueness_score >= 80
                                ? "¡Excelente! Tu título es muy único. Mantén esta diferenciación y considera usarlo como ventaja competitiva."
                                : results.recommendations.competitive_analysis.uniqueness_score >= 60
                                ? "Buen nivel de diferenciación. Considera potenciar los factores únicos identificados para destacar más."
                                : "Tu título necesita más diferenciación. Usa las variaciones virales y específicas por plataforma para destacar."
                              }
                            </p>
                          </div>

                          <div className="p-3 border rounded-md bg-blue-50">
                            <h5 className="text-sm font-medium mb-2">📈 Oportunidades de Mejora</h5>
                            <ul className="text-sm space-y-1">
                              <li>• Analiza títulos de competidores exitosos en tu nicho</li>
                              <li>• Usa las variaciones virales para diferenciarte</li>
                              <li>• Adapta el mensaje según la plataforma específica</li>
                              <li>• Considera A/B testing entre las variaciones propuestas</li>
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Pestaña Detalles */}
                  <TabsContent value="detalles" className="space-y-4">
                    {results.basic_analysis && (
                      <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                        <CardHeader className="pb-2 bg-gray-50">
                          <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                            <FileBarChart className="h-4 w-4 text-blue-600" />
                            Análisis básico
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-2">
                            <li className="flex justify-between items-center text-sm p-2 border rounded-md">
                              <span className="text-muted-foreground">
                                Número de palabras:
                              </span>
                              <span className="font-medium">
                                {results.basic_analysis.word_count}
                              </span>
                            </li>
                            <li className="flex justify-between items-center text-sm p-2 border rounded-md">
                              <span className="text-muted-foreground">
                                Número de caracteres:
                              </span>
                              <span className="font-medium">
                                {results.basic_analysis.char_count}
                              </span>
                            </li>
                            <li className="flex justify-between items-center text-sm p-2 border rounded-md">
                              <span className="text-muted-foreground">
                                Contiene números:
                              </span>
                              <Badge
                                variant={
                                  results.basic_analysis.contains_number
                                    ? "default"
                                    : "outline"
                                }
                              >
                                {results.basic_analysis.contains_number
                                  ? "Sí"
                                  : "No"}
                              </Badge>
                            </li>
                            <li className="flex justify-between items-center text-sm p-2 border rounded-md">
                              <span className="text-muted-foreground">
                                Es una pregunta:
                              </span>
                              <Badge
                                variant={
                                  results.basic_analysis.is_question
                                    ? "default"
                                    : "outline"
                                }
                              >
                                {results.basic_analysis.is_question
                                  ? "Sí"
                                  : "No"}
                              </Badge>
                            </li>
                          </ul>
                        </CardContent>
                      </Card>
                    )}

                    {results.advanced_analysis?.clarity_analysis && (
                      <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                        <CardHeader className="pb-2 bg-gray-50">
                          <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                            <Brain className="h-4 w-4 text-blue-600" />
                            Análisis de claridad
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="mb-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium">
                                Puntuación de claridad
                              </span>
                              <span
                                className={`text-sm font-bold ${getScoreColor(results.advanced_analysis.clarity_analysis.score)}`}
                              >
                                {
                                  results.advanced_analysis.clarity_analysis
                                    .score
                                }
                                %
                              </span>
                            </div>
                            <Progress
                              value={
                                results.advanced_analysis.clarity_analysis.score
                              }
                              className={`h-2 ${
                                results.advanced_analysis.clarity_analysis
                                  .score >= 80
                                  ? "bg-green-100"
                                  : results.advanced_analysis.clarity_analysis
                                        .score >= 60
                                    ? "bg-yellow-100"
                                    : "bg-red-100"
                              }`}
                            />
                          </div>
                          <p className="text-sm">
                            {
                              results.advanced_analysis.clarity_analysis
                                .analysis
                            }
                          </p>
                        </CardContent>
                      </Card>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </>
          ) : (
            <CardContent className="py-12 text-center">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Esperando análisis</h3>
              <p className="text-sm text-muted-foreground">
                Ingresa un título y haz clic en "Analizar título" para obtener
                un análisis detallado.
              </p>
            </CardContent>
          )}
        </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          {!isAuthenticated && process.env.NODE_ENV !== 'development' ? (
            <Card className="border-2 border-gray-200">
              <CardContent className="py-12 text-center">
                <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Inicia sesión para ver tu historial</h3>
                <p className="text-sm text-muted-foreground">
                  Necesitas iniciar sesión para acceder a tu historial de análisis guardados.
                </p>
              </CardContent>
            </Card>
          ) : isLoadingAnalyses ? (
            <Card className="border-2 border-gray-200">
              <CardContent className="py-12 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <h3 className="text-lg font-medium mb-2">Cargando historial...</h3>
                <p className="text-sm text-muted-foreground">
                  Obteniendo tus análisis guardados.
                </p>
              </CardContent>
            </Card>
          ) : recentAnalyses.length === 0 ? (
            <Card className="border-2 border-gray-200">
              <CardContent className="py-12 text-center">
                <FileBarChart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Sin análisis en el historial</h3>
                <p className="text-sm text-muted-foreground">
                  Los análisis se guardan automáticamente cuando completas un análisis.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {recentAnalyses.map((analysis) => (
                <HeadlineAnalysisCard
                  key={analysis.id}
                  analysis={analysis}
                  onLoad={() => handleLoadAnalysis(analysis)}
                  onToggleFavorite={() => handleToggleFavorite(analysis)}
                  onRename={(newName) => handleRename(analysis, newName)}
                  onDelete={() => handleDelete(analysis)}
                  onRegenerate={() => handleRegenerate(analysis)}
                  isRenaming={renamingAnalysisId === analysis.id}
                  onStartRename={() => handleStartRename(analysis)}
                  onCancelRename={handleCancelRename}
                  renameValue={renameValue}
                  onRenameValueChange={setRenameValue}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="favorites" className="space-y-6">
          {!isAuthenticated && process.env.NODE_ENV !== 'development' ? (
            <Card className="border-2 border-gray-200">
              <CardContent className="py-12 text-center">
                <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Inicia sesión para ver tus favoritos</h3>
                <p className="text-sm text-muted-foreground">
                  Necesitas iniciar sesión para acceder a tus análisis favoritos.
                </p>
              </CardContent>
            </Card>
          ) : isLoadingAnalyses ? (
            <Card className="border-2 border-gray-200">
              <CardContent className="py-12 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <h3 className="text-lg font-medium mb-2">Cargando favoritos...</h3>
                <p className="text-sm text-muted-foreground">
                  Obteniendo tus análisis favoritos.
                </p>
              </CardContent>
            </Card>
          ) : favoriteAnalyses.length === 0 ? (
            <Card className="border-2 border-gray-200">
              <CardContent className="py-12 text-center">
                <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Sin análisis favoritos</h3>
                <p className="text-sm text-muted-foreground">
                  Marca análisis como favoritos para verlos aquí. Los favoritos se guardan indefinidamente.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {favoriteAnalyses.map((analysis) => (
                <HeadlineAnalysisCard
                  key={analysis.id}
                  analysis={analysis}
                  onLoad={() => handleLoadAnalysis(analysis)}
                  onToggleFavorite={() => handleToggleFavorite(analysis)}
                  onRename={(newName) => handleRename(analysis, newName)}
                  onDelete={() => handleDelete(analysis)}
                  onRegenerate={() => handleRegenerate(analysis)}
                  isRenaming={renamingAnalysisId === analysis.id}
                  onStartRename={() => handleStartRename(analysis)}
                  onCancelRename={handleCancelRename}
                  renameValue={renameValue}
                  onRenameValueChange={setRenameValue}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
